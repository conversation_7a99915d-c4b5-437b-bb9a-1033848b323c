<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Hand<PERSON> extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Handle open_basedir restriction errors
        if (str_contains($e->getMessage(), 'open_basedir restriction in effect')) {
            // Try to render a simple error page
            try {
                if (view()->exists('errors.500')) {
                    return response()->view('errors.500', [
                        'exception' => $e,
                        'message' => 'A configuration issue has been detected. Please contact support.',
                    ], 500);
                }
                
                // Fallback to a very simple response
                return response('A configuration issue has been detected. Please contact support.', 500);
            } catch (\Throwable $th) {
                // If even that fails, return a basic text response
                return response('An error occurred. Please contact support.', 500);
            }
        }

        return parent::render($request, $e);
    }
}
